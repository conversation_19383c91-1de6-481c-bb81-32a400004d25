<script setup>
import { useUserStore } from "@/store/modules/user"

const userStore = useUserStore()

// 测试通知功能
const testNotifications = () => {
  console.log('当前通知列表:', userStore.notifications)
  console.log('是否有未读通知:', userStore.hasUnreadNotifications)
  console.log('通知加载状态:', userStore.notificationLoading)
}

// 手动刷新通知
const refreshNotifications = () => {
  userStore.fetchNotifications()
}
</script>

<template>
  <div class="app-container">
    <h1>首页</h1>
    <p>欢迎使用江苏省教育厅整治投诉平台</p>

    <div class="notification-test">
      <h3>通知功能测试</h3>
      <el-button @click="testNotifications" type="primary">查看通知状态</el-button>
      <el-button @click="refreshNotifications" type="success">刷新通知</el-button>

      <div class="notification-info">
        <p>通知数量: {{ userStore.notifications.length }}</p>
        <p>未读通知: {{ userStore.hasUnreadNotifications ? '有' : '无' }}</p>
        <p>加载状态: {{ userStore.notificationLoading ? '加载中...' : '已完成' }}</p>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.notification-test {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e2e3e6;
  border-radius: 8px;

  .notification-info {
    margin-top: 15px;

    p {
      margin: 5px 0;
      font-size: 14px;
      color: var(--grey1);
    }
  }
}
</style>
