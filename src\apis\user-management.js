import { request } from "@/utils/axios"

/** 获取用户列表 */
export function getUserListApi(params) {
  return request({
    url: "users",
    method: "get",
    params
  })
}

/** 创建用户 */
export function createUserApi(data) {
  return request({
    url: "users",
    method: "post",
    data
  })
}

/** 更新用户信息 */
export function updateUserApi(id, data) {
  return request({
    url: `users/${id}`,
    method: "put",
    data
  })
}

/** 删除用户 */
export function deleteUserApi(id) {
  return request({
    url: `users/${id}`,
    method: "delete"
  })
}

/** 重置用户密码 */
export function resetUserPasswordApi(id, data) {
  return request({
    url: `users/${id}/reset-password`,
    method: "post",
    data
  })
}

/** 获取用户详情 */
export function getUserDetailApi(id) {
  return request({
    url: `users/${id}`,
    method: "get"
  })
}
