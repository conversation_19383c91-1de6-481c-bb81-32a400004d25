{"name": "jssjytzztspt", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "13.6.0", "axios": "^1.10.0", "cos-js-sdk-v5": "1.10.1", "dayjs": "^1.11.13", "echarts": "5.6.0", "element-plus": "^2.10.4", "js-cookie": "^3.0.5", "lodash-es": "^4.17.21", "mitt": "3.0.1", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "path-browserify": "1.0.1", "path-to-regexp": "8.2.0", "pinia": "^3.0.3", "screenfull": "6.0.2", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@antfu/eslint-config": "^4.17.0", "@vitejs/plugin-vue": "^6.0.0", "eslint": "^9.31.0", "eslint-plugin-format": "^1.0.1", "sass-embedded": "1.78.0", "unocss": "^66.3.3", "unplugin-auto-import": "^19.3.0", "unplugin-svg-component": "^0.12.2", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.4", "vite-svg-loader": "^5.1.0"}}