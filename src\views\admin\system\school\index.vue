<script setup>
import TopTitle from "@/views/components/TopTitle/index.vue";
import { Search, RefreshRight, Plus } from "@element-plus/icons-vue";

// 搜索表单数据
const searchForm = ref({
  schoolType: "", // 学校类型
  city: "", // 市区
  district: "", // 县区
  schoolName: "", // 学校名称
});

// 学校类型选项
const schoolTypeOptions = [
  { label: "全部", value: "" },
  { label: "大学", value: "university" },
  { label: "初中", value: "middle" },
  { label: "高中", value: "high" },
  { label: "小学", value: "primary" },
];

// 市区选项
const cityOptions = [
  { label: "全部", value: "" },
  { label: "南京市", value: "nanjing" },
  { label: "苏州市", value: "suzhou" },
  { label: "无锡市", value: "wuxi" },
  { label: "常州市", value: "changzhou" },
  { label: "镇江市", value: "zhenjiang" },
  { label: "扬州市", value: "yangzhou" },
  { label: "泰州市", value: "taizhou" },
  { label: "南通市", value: "nantong" },
  { label: "盐城市", value: "yancheng" },
  { label: "淮安市", value: "huaian" },
  { label: "宿迁市", value: "suqian" },
  { label: "连云港市", value: "lianyungang" },
  { label: "徐州市", value: "xuzhou" },
];

// 县区选项
const districtOptions = [
  { label: "全部", value: "" },
  { label: "鼓楼区", value: "gulou" },
  { label: "玄武区", value: "xuanwu" },
  { label: "建邺区", value: "jianye" },
  { label: "秦淮区", value: "qinhuai" },
  { label: "雨花台区", value: "yuhuatai" },
  { label: "栖霞区", value: "qixia" },
  { label: "浦口区", value: "pukou" },
  { label: "六合区", value: "liuhe" },
  { label: "溧水区", value: "lishui" },
  { label: "高淳区", value: "gaochun" },
];

// 搜索功能
const handleSearch = async () => {
  try {
    console.log("搜索参数:", searchForm.value);
    // 构建查询参数
    const params = {
      schoolType: searchForm.value.schoolType,
      city: searchForm.value.city,
      district: searchForm.value.district,
      schoolName: searchForm.value.schoolName,
      page: pagination.value.currentPage,
      pageSize: pagination.value.pageSize,
    };

    // 调用API获取学校列表
    // const { data } = await getSchoolListApi(params);
    // tableData.value = data.list;
    // pagination.value.total = data.total;

    ElMessage.success("搜索完成");
  } catch (error) {
    ElMessage.error("搜索失败");
    console.error("搜索错误:", error);
  }
};

// 重置功能
const handleReset = () => {
  searchForm.value = {
    schoolType: "",
    city: "",
    district: "",
    schoolName: "",
  };
  console.log("表单已重置");
  // 重置后重新搜索
  handleSearch();
};

// 表格数据
const tableData = ref([
  {
    id: "10001",
    schoolName: "南京市第一中学",
    schoolType: "大学",
    city: "南京市",
    district: "鼓楼区",
    createTime: "2025-07-22",
  },
  {
    id: "10002",
    schoolName: "南京市第一中学",
    schoolType: "初中",
    city: "南京市",
    district: "鼓楼区",
    createTime: "2025-07-01",
  },
  {
    id: "10003",
    schoolName: "南京市第一中学",
    schoolType: "高中",
    city: "南京市",
    district: "鼓楼区",
    createTime: "2025-05-31",
  },
  {
    id: "10004",
    schoolName: "南京市第一中学",
    schoolType: "初中",
    city: "南京市",
    district: "鼓楼区",
    createTime: "2025-05-30",
  },
  {
    id: "10005",
    schoolName: "南京市第一中学",
    schoolType: "大学",
    city: "南京市",
    district: "鼓楼区",
    createTime: "2025-05-28",
  },
  {
    id: "10006",
    schoolName: "南京市第一中学",
    schoolType: "小学",
    city: "南京市",
    district: "鼓楼区",
    createTime: "2025-05-17",
  },
  {
    id: "10007",
    schoolName: "南京市第一中学",
    schoolType: "大学",
    city: "南京市",
    district: "鼓楼区",
    createTime: "2025-05-09",
  },
  {
    id: "10008",
    schoolName: "南京市第一中学",
    schoolType: "小学",
    city: "南京市",
    district: "鼓楼区",
    createTime: "2025-05-08",
  },
  {
    id: "10009",
    schoolName: "南京市第一中学",
    schoolType: "大学",
    city: "南京市",
    district: "鼓楼区",
    createTime: "2025-04-30",
  },
]);

// 分页数据
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 50,
});

// 分页变化处理
const handleCurrentChange = (page) => {
  pagination.value.currentPage = page;
  console.log("当前页:", page);
};

// 刷新数据
const handleRefresh = () => {
  console.log("刷新数据");
  handleSearch();
};

// 弹窗相关状态
const dialogVisible = ref(false);
const dialogTitle = ref("");
const isEdit = ref(false);

// 学校表单数据
const schoolForm = ref({
  schoolName: "",
  schoolType: "",
  city: "",
  district: "",
});

// 学校表单验证规则
const schoolFormRules = {
  schoolName: [{ required: true, message: "请输入学校名称", trigger: "blur" }],
  schoolType: [
    { required: true, message: "请选择学校类型", trigger: "change" },
  ],
  city: [{ required: true, message: "请选择市区", trigger: "change" }],
  district: [{ required: true, message: "请选择县区", trigger: "change" }],
};

// 表单引用
const schoolFormRef = ref();

// 新增学校
const handleAddSchool = () => {
  dialogTitle.value = "新增学校";
  isEdit.value = false;
  schoolForm.value = {
    schoolName: "",
    schoolType: "",
    city: "",
    district: "",
  };
  dialogVisible.value = true;
};

// 导出数据
const handleExport = () => {
  console.log("导出数据");
  ElMessage.success("数据导出成功");
};

// 编辑学校
const handleEditSchool = (row) => {
  dialogTitle.value = "编辑学校";
  isEdit.value = true;
  schoolForm.value = {
    id: row.id,
    schoolName: row.schoolName,
    schoolType: row.schoolType,
    city: getCityValue(row.city),
    district: getDistrictValue(row.district),
  };
  dialogVisible.value = true;
};

// 获取市区值
const getCityValue = (cityLabel) => {
  const city = cityOptions.find((c) => c.label === cityLabel);
  return city ? city.value : "";
};

// 获取县区值
const getDistrictValue = (districtLabel) => {
  const district = districtOptions.find((d) => d.label === districtLabel);
  return district ? district.value : "";
};

// 关闭弹窗
const handleCloseDialog = () => {
  dialogVisible.value = false;
  schoolFormRef.value?.resetFields();
};

// 提交表单
const handleSubmitForm = async () => {
  try {
    await schoolFormRef.value.validate();

    if (isEdit.value) {
      // 编辑学校逻辑
      console.log("编辑学校:", schoolForm.value);
      ElMessage.success("学校信息修改成功");
    } else {
      // 新增学校逻辑
      console.log("新增学校:", schoolForm.value);
      ElMessage.success("学校添加成功");
    }

    handleCloseDialog();
    // 刷新列表
    handleSearch();
  } catch (error) {
    console.error("表单验证失败:", error);
  }
};

// 删除学校
const handleDeleteSchool = (row) => {
  ElMessageBox.confirm(`确定要删除学校"${row.schoolName}"吗？`, "删除确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    customClass: "custom-confirm-box",
    confirmButtonClass: "confirm-btn",
    cancelButtonClass: "cancel-btn",
  })
    .then(() => {
      // 这里调用删除API
      console.log("删除学校:", row.id);
      ElMessage.success("学校删除成功");
      // 刷新列表
      handleSearch();
    })
    .catch(() => {
      ElMessage.info("已取消删除");
    });
};

// 操作处理
const handleOperation = async (row, action) => {
  if (action === "edit") {
    handleEditSchool(row);
  } else if (action === "delete") {
    handleDeleteSchool(row);
  }
};
</script>

<template>
  <div class="complaint-distribution app-container">
    <TopTitle title="学校管理"></TopTitle>
    <section class="search-form px-[24px] py-[16px] rounded-[2px] bg-[#fff]">
      <el-form :model="searchForm" class="search-form-4" label-position="top">
        <!-- 学校类型选择 -->
        <el-form-item label="学校类型">
          <el-select v-model="searchForm.schoolType" placeholder="全部">
            <el-option
              v-for="item in schoolTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <!-- 市区选择 -->
        <el-form-item label="市区">
          <el-select v-model="searchForm.city" placeholder="全部">
            <el-option
              v-for="item in cityOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <!-- 县区选择 -->
        <el-form-item label="县区">
          <el-select v-model="searchForm.district" placeholder="全部">
            <el-option
              v-for="item in districtOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <!-- 学校名称搜索 -->
        <el-form-item label="学校名称">
          <el-input
            v-model="searchForm.schoolName"
            placeholder="请输入学校名称"
          />
        </el-form-item>

        <!-- 按钮组 - 第二行右对齐 -->
        <div class="button-row">
          <el-button class="common-button-3" @click="handleSearch">
            <template #icon>
              <el-icon>
                <Search />
              </el-icon>
            </template>
            查询
          </el-button>
          <el-button class="reset-button" @click="handleReset">
            <template #icon>
              <el-icon><RefreshRight /></el-icon>
            </template>
            重置
          </el-button>
        </div>
      </el-form>
    </section>
    <section
      class="table-container mt-[16px] px-[24px] py-[16px] rounded-[2px] bg-[#fff]"
    >
      <!-- 操作按钮 -->
      <div class="mb-[16px] flex justify-between">
        <el-button class="common-button-4" @click="handleRefresh">
          <template #icon>
            <el-icon><RefreshRight /></el-icon>
          </template>
          刷新
        </el-button>
        <div class="flex gap-[16px]">
          <el-button class="common-button-3" @click="handleAddSchool">
            <template #icon>
              <el-icon><Plus /></el-icon>
            </template>
            新增学校
          </el-button>
          <el-button class="common-button-3" @click="handleExport">
            <template #icon>
              <img
                src="@/assets/images/common/export.png"
                alt=""
                width="16"
                height="16"
              />
            </template>
            批量导入
          </el-button>
        </div>
      </div>

      <!-- 表格 -->
      <el-table
        :data="tableData"
        style="width: 100%"
        class="custom-table"
        stripe
      >
        <!-- 序号 -->
        <el-table-column prop="id" label="序号" width="80" align="center" />

        <!-- 学校名称 -->
        <el-table-column
          prop="schoolName"
          label="学校名称"
          min-width="200"
          align="center"
        />

        <!-- 学校类型 -->
        <el-table-column
          prop="schoolType"
          label="学校类型"
          min-width="120"
          align="center"
        />

        <!-- 市区 -->
        <el-table-column
          prop="city"
          label="市区"
          min-width="120"
          align="center"
        />

        <!-- 县区 -->
        <el-table-column
          prop="district"
          label="县区"
          min-width="120"
          align="center"
        />

        <!-- 创建时间 -->
        <el-table-column
          prop="createTime"
          label="创建时间"
          min-width="140"
          align="center"
        />

        <!-- 操作列 -->
        <el-table-column label="操作" width="200" align="center">
          <template #default="{ row }">
            <div class="flex-center-center gap-[16px]">
              <div class="pointer blue2" @click="handleOperation(row, 'edit')">
                编辑
              </div>
              <div class="pointer red1" @click="handleOperation(row, 'delete')">
                删除
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="mt-[16px] flex justify-end">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          :total="pagination.total"
          layout="total, prev, pager, next"
          @current-change="handleCurrentChange"
        />
      </div>
    </section>

    <!-- 学校弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="480px"
      class="custom-dialog"
      :before-close="handleCloseDialog"
    >
      <el-form
        ref="schoolFormRef"
        :model="schoolForm"
        :rules="schoolFormRules"
        label-position="top"
        class="school-form"
      >
        <el-form-item label="学校名称" prop="schoolName" required>
          <el-input
            v-model="schoolForm.schoolName"
            placeholder="请输入学校名称"
            maxlength="50"
          />
        </el-form-item>

        <el-form-item label="学校类型" prop="schoolType" required>
          <el-select
            v-model="schoolForm.schoolType"
            placeholder="请选择学校类型"
            style="width: 100%"
          >
            <el-option
              v-for="item in schoolTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="市区" prop="city" required>
          <el-select
            v-model="schoolForm.city"
            placeholder="请选择市区"
            style="width: 100%"
          >
            <el-option
              v-for="item in cityOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="县区" prop="district" required>
          <el-select
            v-model="schoolForm.district"
            placeholder="请选择县区"
            style="width: 100%"
          >
            <el-option
              v-for="item in districtOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button class="cancel-btn" @click="handleCloseDialog"
            >取消</el-button
          >
          <el-button class="confirm-btn" @click="handleSubmitForm"
            >确认</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.hidden-label {
  :deep(.el-form-item__label) {
    display: none;
  }
}

.whitespace-pre-line {
  white-space: pre-line;
  font-size: 16px;
  line-height: 24px;
}

// 学校表单样式
.school-form {
  .el-form-item {
    margin-bottom: 20px;

    .el-form-item__label {
      color: var(--grey1);
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      margin-bottom: 8px;
    }

    .el-input__wrapper {
      height: 48px;
      font-size: 16px;
    }

    .el-select {
      .el-select__wrapper {
        height: 48px;
        font-size: 16px;
      }
    }
  }
}
</style>
